{
  "batch_configs": [
    {
      // ========== 基础配置参数 ==========
      "处理模式": 1,
      // 必需参数，处理模式选择
      // 1 = 单阶段处理（调用one_stage_test）
      // 2 = 双阶段不发图片（调用test+test2）
      // 3 = 双阶段发图片（调用test+test3）

      "模型ID": 1,
      // 可选参数，默认值：1
      // 1 = doubao-seed-1-6-250615
      // 2 = doubao-seed-1-6-flash-250715
      // 3 = doubao-1-5-thinking-vision-pro-250428
      // 4 = doubao-1-5-vision-pro-32k-250115

      "题型": 13,
      // 必需参数，无默认值
      // 题型编号，对应types文件夹中的题型配置
      // 1=涂卡选择题, 2=涂卡判断题, 3=连线题, 4=图表题, 5=翻译题
      // 6=画图题, 7=数学应用题, 8=数学计算题, 9=简单的四则运算
      // 10=填空题, 11=判断题, 12=多选题, 13=单选题

      "图像文件夹": 1,
      // 可选参数，默认值：1
      // 指定要处理的图像文件夹编号
      // 1=images, 2=OpenCV_result, 3=grounding_result, 4=YOLO_result
      // 5=YOLO_text_result, 6=manual_result, 7=roboflow_yolo_result

      // ========== 双阶段模式专用参数 ==========
      "round2批改模式": 2,
      // 可选参数，默认值：2，仅在处理模式=2时有效
      // 1 = 使用大模型进行批改
      // 2 = 使用JSON比对进行批改

      // ========== 图像处理参数 ==========
      "像素增强": "y",
      // 可选参数，默认值："n"
      // "y" = 启用像素增强（图像预处理）
      // "n" = 不启用像素增强

      "灰度阀门": 150,
      // 可选参数，无默认值（仅在像素增强="y"时生效）
      // 取值范围：0-255
      // 用于图像二值化处理的阈值
      // 如果像素增强="n"，此参数将被忽略
      // 如果像素增强="y"但未指定此参数，将使用默认值200

      "像素粘连": "n",
      // 可选参数，默认值："n"
      // "y" = 启用像素粘连处理
      // "n" = 不启用像素粘连处理

      "图像放大倍数": 2,
      // 可选参数，默认值：1
      // 图像放大倍数，支持小数（如1.5、2.0、4.0等）

      // ========== API调用参数 ==========
      "response_format": 1,
      // 可选参数，默认值：1
      // 1 = text格式响应
      // 2 = json_object格式响应

      "temperature": 0.8,
      // 可选参数，无默认值
      // 控制AI响应的随机性，取值范围：0.0-2.0
      // 值越小越确定，值越大越随机

      "top_p": 0.9,
      // 可选参数，无默认值
      // 核采样参数，取值范围：0.0-1.0
      // 控制AI考虑的词汇范围

      "max_tokens": 8192,
      // 可选参数，无默认值
      // 限制AI响应的最大token数量

      // ========== 自定义提示词参数 ==========
      "one_stage_test_prompt": "你是一位严谨负责的资深阅卷老师...",
      // 可选参数，仅在处理模式=1时使用
      // 单阶段处理的自定义提示词
      // 支持两种格式：
      // 1. 直接字符串内容（如上面的示例）
      // 2. 以.md结尾的文件名，系统会从batch_configs/prompt目录读取对应的md文件
      // 例如："one_stage_test_prompt": "single_stage.md"
      // 如果不指定，将使用默认提示词

      "test_prompt": "你是一位严谨负责的资深阅卷老师...",
      // 可选参数，仅在处理模式=2或3时使用
      // 双阶段处理第一阶段的自定义提示词（识别答案）
      // 支持两种格式：
      // 1. 直接字符串内容（如上面的示例）
      // 2. 以.md结尾的文件名，系统会从batch_configs/prompt目录读取对应的md文件
      // 例如："test_prompt": "first_stage.md"
      // 如果不指定，将使用默认提示词

      "test2_prompt": "你是一位严谨负责的资深阅卷老师...",
      // 可选参数，仅在处理模式=2时使用
      // 双阶段处理第二阶段的自定义提示词（不发图片的批改）
      // 支持两种格式：
      // 1. 直接字符串内容（如上面的示例）
      // 2. 以.md结尾的文件名，系统会从batch_configs/prompt目录读取对应的md文件
      // 例如："test2_prompt": "second_stage_no_image.md"
      // 如果不指定，将使用默认提示词

      "test3_prompt": "你是一位严谨负责的资深阅卷老师..."
      // 可选参数，仅在处理模式=3时使用
      // 双阶段处理第二阶段的自定义提示词（发图片的批改）
      // 支持两种格式：
      // 1. 直接字符串内容（如上面的示例）
      // 2. 以.md结尾的文件名，系统会从batch_configs/prompt目录读取对应的md文件
      // 例如："test3_prompt": "second_stage_with_image.md"
      // 如果不指定，将使用默认提示词
    },
    {
      // ========== 第二个配置示例（双阶段不发图片模式） ==========
      "处理模式": 2,
      "round2批改模式": 1,
      "模型ID": 2,
      "response_format": 2,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "n",
      "像素粘连": "n",
      "图像放大倍数": 1,
      "temperature": 0.5,
      "top_p": 0.8,
      "test_prompt": "自定义第一阶段提示词...",
      "test2_prompt": "自定义第二阶段提示词（不发图片）..."
    },
    {
      // ========== 第三个配置示例（双阶段发图片模式） ==========
      "处理模式": 3,
      "模型ID": 1,
      "response_format": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "y",
      "灰度阀门": 150,
      "像素粘连": "n",
      "图像放大倍数": 2,
      "temperature": 0.8,
      "top_p": 0.9,
      "test_prompt": "自定义第一阶段提示词...",
      "test3_prompt": "自定义第二阶段提示词（发图片）..."
    },
    {
      // ========== 第四个配置示例（使用md文件格式的prompt） ==========
      "处理模式": 1,
      "题型": 1,
      "图像文件夹": 2,
      "像素增强": "y",
      "灰度阀门": 200,
      "one_stage_test_prompt": "single_stage_custom.md"
      // 使用md文件格式的prompt，系统会从batch_configs/prompt目录读取single_stage_custom.md文件
    },
    {
      // ========== 第五个配置示例（最小配置） ==========
      "题型": 1
      // 只指定必需参数，其他参数将使用默认值
      // 处理模式: 1（单阶段）
      // 模型ID: 1
      // 图像文件夹: 1
      // 像素增强: "n"
      // 像素粘连: "n"
      // 图像放大倍数: 1
      // response_format: 1
    }
  ]

  // ========== 配置文件说明 ==========
  // 1. 新增功能：支持从md文件读取prompt
  //    - 在batch_configs/prompt目录下创建.md文件
  //    - 在配置中使用文件名（如"test_prompt": "my_prompt.md"）
  //    - 系统会自动读取文件内容并替换到配置中
  //    - 如果md文件不存在，会显示警告并使用默认prompt
  //
  // 2. 配置副本功能：
  //    - 当配置中包含md格式的prompt时，系统会自动创建配置副本
  //    - 副本文件保存在batch_configs/batch_configs_copy目录
  //    - 副本中的md文件名会被替换为实际的文本内容
  //
  // 3. 参数验证和默认值：
  //    - 系统会为大部分参数应用默认值（除了"题型"字段）
  //    - "题型"是唯一的必需参数，必须明确指定
  //    - 其他参数如果未指定，会使用合理的默认值
}
